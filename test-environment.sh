#!/bin/bash

# 环境测试脚本
# 用于验证所有必要的环境组件是否正确配置

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[✓]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[!]${NC} $1"
}

log_error() {
    echo -e "${RED}[✗]${NC} $1"
}

log_test() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

echo "=========================================="
echo "      环境测试脚本"
echo "=========================================="
echo ""

# 测试Java环境
log_test "测试Java环境..."
if command -v jenv &> /dev/null; then
    log_info "jenv已安装: $(jenv version)"
    
    # 设置Java环境
    export JAVA_HOME=$(jenv javahome zulu64-22.0.2 2>/dev/null || jenv javahome)
    export PATH="$JAVA_HOME/bin:$PATH"
    
    if java -version &> /dev/null; then
        log_info "Java版本: $(java -version 2>&1 | head -n 1)"
    else
        log_error "Java未正确配置"
    fi
else
    log_warn "jenv未安装，使用系统默认Java"
    if java -version &> /dev/null; then
        log_info "Java版本: $(java -version 2>&1 | head -n 1)"
    else
        log_error "Java未安装"
    fi
fi

# 测试Maven
log_test "测试Maven..."
if mvn -version &> /dev/null; then
    log_info "Maven版本: $(mvn -version | head -n 1)"
else
    log_error "Maven未安装"
fi

# 测试Node.js
log_test "测试Node.js..."
if node -v &> /dev/null; then
    log_info "Node.js版本: $(node -v)"
else
    log_error "Node.js未安装"
fi

if npm -v &> /dev/null; then
    log_info "npm版本: $(npm -v)"
else
    log_error "npm未安装"
fi

# 测试MySQL
log_test "测试MySQL..."
MYSQL_PATH="/usr/local/mysql-8.4.6-macos15-arm64/bin/mysql"
if [ -f "$MYSQL_PATH" ]; then
    export PATH="/usr/local/mysql-8.4.6-macos15-arm64/bin:$PATH"
    log_info "MySQL已安装: $($MYSQL_PATH --version)"
    
    # 检查MySQL服务状态
    if pgrep -x "mysqld" > /dev/null; then
        log_info "MySQL服务正在运行"
    else
        log_warn "MySQL服务未运行"
        echo "  启动命令: sudo /usr/local/mysql-8.4.6-macos15-arm64/support-files/mysql.server start"
    fi
else
    log_error "MySQL未找到在预期路径: $MYSQL_PATH"
fi

# 测试端口占用情况
log_test "检查端口占用情况..."
ports=(8848 8081 8082 8083 8084 8085 8086 8087 8090)
port_names=("Nacos" "User-Service" "Course-Service" "Learning-Service" "Recommendation-Service" "Discussion-Service" "Frontend" "Payment-Service" "Gateway-Service")

for i in "${!ports[@]}"; do
    port=${ports[$i]}
    name=${port_names[$i]}
    
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        log_warn "端口 $port 已被占用 ($name)"
    else
        log_info "端口 $port 可用 ($name)"
    fi
done

# 测试项目结构
log_test "检查项目结构..."
required_dirs=("user-service" "course-service" "learning-service" "recommendation-service" "discussion-service" "payment-service" "gateway-service" "frontend" "infrastructure/nacos")
required_files=("pom.xml" "infrastructure/database/init.sql")

for dir in "${required_dirs[@]}"; do
    if [ -d "$dir" ]; then
        log_info "目录存在: $dir"
    else
        log_error "目录缺失: $dir"
    fi
done

for file in "${required_files[@]}"; do
    if [ -f "$file" ]; then
        log_info "文件存在: $file"
    else
        log_error "文件缺失: $file"
    fi
done

# 测试前端依赖
log_test "检查前端依赖..."
if [ -d "frontend/node_modules" ]; then
    log_info "前端依赖已安装"
else
    log_warn "前端依赖未安装，运行: cd frontend && npm install"
fi

# 测试Maven编译
log_test "测试Maven编译..."
if mvn compile -DskipTests=true -q &> /dev/null; then
    log_info "Maven编译成功"
else
    log_warn "Maven编译失败，可能需要运行: mvn clean compile"
fi

echo ""
echo "=========================================="
echo "      测试完成"
echo "=========================================="
echo ""
echo "如果所有测试都通过，可以运行以下命令启动系统："
echo "1. ./setup-database.sh      # 设置数据库（首次运行）"
echo "2. ./start-all-services.sh  # 启动所有服务"
echo ""
echo "如果有警告或错误，请根据提示进行修复。"
