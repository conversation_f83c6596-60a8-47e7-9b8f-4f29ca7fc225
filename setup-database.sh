#!/bin/bash

# 数据库设置脚本 (macOS版本)
# 用于安装和配置MySQL数据库

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 数据库配置
DB_NAME="learning_platform"
DB_USER="study250801"
DB_PASSWORD="@yw@%K!@3^Dm"

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查Homebrew
check_homebrew() {
    if ! command -v brew &> /dev/null; then
        log_error "Homebrew未安装，请先安装Homebrew:"
        echo "  /bin/bash -c \"\$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\""
        exit 1
    fi
    log_info "Homebrew已安装"
}

# 安装MySQL
install_mysql() {
    log_step "检查MySQL安装状态..."
    
    if command -v mysql &> /dev/null; then
        log_info "MySQL已安装: $(mysql --version)"
        return 0
    fi
    
    log_step "安装MySQL..."
    brew install mysql
    
    log_step "启动MySQL服务..."
    brew services start mysql
    
    log_info "MySQL安装完成"
}

# 配置MySQL
configure_mysql() {
    log_step "配置MySQL..."
    
    # 等待MySQL启动
    sleep 5
    
    # 检查MySQL是否运行
    if ! pgrep -x "mysqld" > /dev/null; then
        log_info "启动MySQL服务..."
        brew services start mysql
        sleep 5
    fi
    
    # 设置root密码（如果需要）
    log_info "MySQL配置提示："
    echo "1. 如果这是首次安装，root用户可能没有密码"
    echo "2. 建议运行 mysql_secure_installation 来设置安全配置"
    echo "3. 当前脚本将尝试创建数据库和用户"
}

# 创建数据库和用户
create_database() {
    log_step "创建数据库和用户..."
    
    # 尝试连接MySQL（无密码）
    if mysql -u root -e "SELECT 1;" 2>/dev/null; then
        log_info "使用root用户（无密码）连接MySQL"
        MYSQL_CMD="mysql -u root"
    else
        # 提示输入root密码
        log_info "请输入MySQL root密码："
        read -s ROOT_PASSWORD
        MYSQL_CMD="mysql -u root -p$ROOT_PASSWORD"
        
        # 测试连接
        if ! $MYSQL_CMD -e "SELECT 1;" 2>/dev/null; then
            log_error "无法连接到MySQL，请检查root密码"
            exit 1
        fi
    fi
    
    log_info "创建数据库: $DB_NAME"
    $MYSQL_CMD -e "CREATE DATABASE IF NOT EXISTS $DB_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
    
    log_info "创建用户: $DB_USER"
    $MYSQL_CMD -e "CREATE USER IF NOT EXISTS '$DB_USER'@'localhost' IDENTIFIED BY '$DB_PASSWORD';"
    
    log_info "授权用户访问数据库"
    $MYSQL_CMD -e "GRANT ALL PRIVILEGES ON $DB_NAME.* TO '$DB_USER'@'localhost';"
    $MYSQL_CMD -e "FLUSH PRIVILEGES;"
    
    log_info "数据库和用户创建完成"
}

# 导入初始数据
import_initial_data() {
    log_step "导入初始数据..."
    
    if [ ! -f "infrastructure/database/init.sql" ]; then
        log_error "未找到初始化SQL文件: infrastructure/database/init.sql"
        exit 1
    fi
    
    log_info "导入表结构和初始数据..."
    mysql -u $DB_USER -p$DB_PASSWORD $DB_NAME < infrastructure/database/init.sql
    
    if [ $? -eq 0 ]; then
        log_info "初始数据导入成功"
    else
        log_error "初始数据导入失败"
        exit 1
    fi
}

# 测试数据库连接
test_connection() {
    log_step "测试数据库连接..."
    
    if mysql -u $DB_USER -p$DB_PASSWORD -e "USE $DB_NAME; SHOW TABLES;" > /dev/null 2>&1; then
        log_info "数据库连接测试成功"
        
        # 显示表信息
        echo ""
        echo "=== 数据库表列表 ==="
        mysql -u $DB_USER -p$DB_PASSWORD -e "USE $DB_NAME; SHOW TABLES;"
        echo ""
    else
        log_error "数据库连接测试失败"
        exit 1
    fi
}

# 显示配置信息
show_config() {
    echo ""
    echo "=== 数据库配置信息 ==="
    echo "数据库名: $DB_NAME"
    echo "用户名: $DB_USER"
    echo "密码: $DB_PASSWORD"
    echo "连接URL: ************************************"
    echo ""
    echo "=== 管理命令 ==="
    echo "连接数据库: mysql -u $DB_USER -p$DB_PASSWORD $DB_NAME"
    echo "启动MySQL: brew services start mysql"
    echo "停止MySQL: brew services stop mysql"
    echo "重启MySQL: brew services restart mysql"
    echo ""
}

# 主函数
main() {
    echo "=========================================="
    echo "      MySQL数据库设置脚本 (macOS)"
    echo "=========================================="
    echo ""
    
    case "${1:-setup}" in
        "install")
            check_homebrew
            install_mysql
            configure_mysql
            ;;
        "create")
            create_database
            ;;
        "import")
            import_initial_data
            ;;
        "test")
            test_connection
            ;;
        "config")
            show_config
            ;;
        "setup"|"")
            check_homebrew
            install_mysql
            configure_mysql
            create_database
            import_initial_data
            test_connection
            show_config
            ;;
        *)
            echo "用法: $0 [setup|install|create|import|test|config]"
            echo "  setup   - 完整设置 (默认)"
            echo "  install - 仅安装MySQL"
            echo "  create  - 仅创建数据库和用户"
            echo "  import  - 仅导入初始数据"
            echo "  test    - 仅测试连接"
            echo "  config  - 显示配置信息"
            exit 1
            ;;
    esac
    
    log_info "数据库设置完成！"
}

# 运行主函数
main "$@"
