# SpringCloud微服务学习平台 - 快速启动指南 (macOS)

## 🚀 一键启动

### 方式一：完整启动（推荐）

```bash
# 1. 设置数据库（首次运行）
./setup-database.sh

# 2. 启动所有服务
./start-all-services.sh
```

### 方式二：仅使用H2内存数据库启动

```bash
# 直接启动（使用H2内存数据库）
./start-all-services.sh
```

## 📋 系统要求

- **操作系统**: macOS 10.15+
- **Java**: 17+ (推荐使用OpenJDK)
- **Maven**: 3.6+
- **Node.js**: 16+
- **MySQL**: 8.0+ (可选，不安装则使用H2内存数据库)

## 🔧 环境准备

### 1. 安装基础环境

```bash
# 安装Homebrew（如果未安装）
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# 安装Java
brew install openjdk@17

# 安装Maven
brew install maven

# 安装Node.js
brew install node

# 安装MySQL（可选）
brew install mysql
```

### 2. 验证环境

```bash
java -version    # 应显示Java 17+
mvn -version     # 应显示Maven 3.6+
node -v          # 应显示Node.js 16+
npm -v           # 应显示npm版本
```

## 🗄️ 数据库配置

### 选项1：使用MySQL（推荐生产环境）

```bash
# 运行数据库设置脚本
./setup-database.sh

# 手动设置（如果脚本失败）
mysql -u root -p
CREATE DATABASE learning_platform CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'study250801'@'localhost' IDENTIFIED BY '@yw@%K!@3^Dm';
GRANT ALL PRIVILEGES ON learning_platform.* TO 'study250801'@'localhost';
FLUSH PRIVILEGES;
EXIT;

# 导入初始数据
mysql -u study250801 -p'@yw@%K!@3^Dm' learning_platform < infrastructure/database/init.sql
```

### 选项2：使用H2内存数据库（开发环境）

无需额外配置，系统会自动使用H2内存数据库。

## 🚀 启动服务

### 使用一键启动脚本

```bash
# 启动所有服务
./start-all-services.sh

# 查看服务状态
./start-all-services.sh status

# 停止所有服务
./start-all-services.sh stop

# 重启所有服务
./start-all-services.sh restart
```

### 手动启动（调试用）

```bash
# 1. 启动Nacos
cd infrastructure/nacos/nacos/bin
./startup.sh -m standalone
cd -

# 2. 启动各微服务（在不同终端窗口）
cd user-service && mvn spring-boot:run
cd course-service && mvn spring-boot:run
cd learning-service && mvn spring-boot:run
cd recommendation-service && mvn spring-boot:run
cd discussion-service && mvn spring-boot:run
cd payment-service && mvn spring-boot:run
cd gateway-service && mvn spring-boot:run

# 3. 启动前端
cd frontend && npm run serve
```

## 🌐 访问地址

启动成功后，可以通过以下地址访问：

- **前端应用**: http://localhost:8086
- **API网关**: http://localhost:8090
- **Nacos控制台**: http://localhost:8848/nacos (用户名/密码: nacos/nacos)

### 微服务端口分配

| 服务名称 | 端口 | 描述 |
|---------|------|------|
| Nacos | 8848 | 服务注册中心 |
| User Service | 8081 | 用户服务 |
| Course Service | 8082 | 课程服务 |
| Learning Service | 8083 | 学习服务 |
| Recommendation Service | 8084 | 推荐服务 |
| Discussion Service | 8085 | 讨论服务 |
| Payment Service | 8087 | 支付服务 |
| Gateway Service | 8090 | API网关 |
| Frontend | 8086 | 前端应用 |

## 📊 健康检查

```bash
# 检查所有服务状态
./start-all-services.sh status

# 手动检查各服务健康状态
curl http://localhost:8090/actuator/health  # 网关
curl http://localhost:8081/actuator/health  # 用户服务
curl http://localhost:8082/actuator/health  # 课程服务
# ... 其他服务类似
```

## 📝 日志查看

```bash
# 查看所有服务日志
tail -f *.log

# 查看特定服务日志
tail -f user-service.log
tail -f gateway-service.log
tail -f frontend.log
```

## 🔧 故障排查

### 常见问题

1. **端口被占用**
   ```bash
   # 查看端口占用
   lsof -i :8080
   
   # 杀死占用进程
   kill -9 <PID>
   ```

2. **Java版本问题**
   ```bash
   # 检查Java版本
   java -version
   
   # 如果版本不对，设置JAVA_HOME
   export JAVA_HOME=/usr/libexec/java_home -v 17
   ```

3. **MySQL连接失败**
   ```bash
   # 检查MySQL状态
   brew services list | grep mysql
   
   # 启动MySQL
   brew services start mysql
   
   # 重置MySQL密码
   mysql_secure_installation
   ```

4. **前端依赖问题**
   ```bash
   cd frontend
   rm -rf node_modules package-lock.json
   npm install
   ```

5. **Nacos启动失败**
   ```bash
   # 检查Java版本兼容性
   cd infrastructure/nacos/nacos/bin
   ./startup.sh -m standalone
   
   # 查看Nacos日志
   tail -f ../logs/start.out
   ```

### 清理和重置

```bash
# 停止所有服务
./start-all-services.sh stop

# 清理编译文件
mvn clean

# 清理前端依赖
cd frontend && rm -rf node_modules package-lock.json

# 重新开始
./start-all-services.sh
```

## 🧪 测试验证

### API测试

```bash
# 测试网关
curl http://localhost:8090/actuator/health

# 测试用户登录
curl -X POST http://localhost:8090/api/user/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'

# 测试课程列表
curl http://localhost:8090/api/course/list
```

### 前端测试

1. 打开浏览器访问 http://localhost:8086
2. 尝试登录（用户名: admin, 密码: admin123）
3. 浏览课程列表
4. 测试各个功能模块

## 📞 技术支持

如果遇到问题，请检查：

1. **环境要求**是否满足
2. **端口冲突**是否存在
3. **日志文件**中的错误信息
4. **网络连接**是否正常

## 🔄 更新和维护

```bash
# 更新代码后重新启动
git pull
./start-all-services.sh restart

# 更新前端依赖
cd frontend && npm update

# 更新Maven依赖
mvn clean compile
```

---

**注意**: 首次启动可能需要较长时间来下载依赖和编译项目，请耐心等待。
