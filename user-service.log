[INFO] Scanning for projects...
[INFO] 
[INFO] -----------------< com.learningplatform:user-service >------------------
[INFO] Building User Service 1.0.0
[INFO]   from pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] >>> spring-boot:3.2.0:run (default-cli) > test-compile @ user-service >>>
[WARNING] The artifact mysql:mysql-connector-java:jar:8.0.33 has been relocated to com.mysql:mysql-connector-j:jar:8.0.33: MySQL Connector/J artifacts moved to reverse-DNS compliant Maven 2+ coordinates.
[INFO] 
[INFO] --- resources:3.3.1:resources (default-resources) @ user-service ---
[INFO] Copying 1 resource from src/main/resources to target/classes
[INFO] 
[INFO] --- compiler:3.13.0:compile (default-compile) @ user-service ---
[INFO] Nothing to compile - all classes are up to date.
[INFO] 
[INFO] --- resources:3.3.1:testResources (default-testResources) @ user-service ---
[INFO] skip non existing resourceDirectory /Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/user-service/src/test/resources
[INFO] 
[INFO] --- compiler:3.13.0:testCompile (default-testCompile) @ user-service ---
[INFO] Nothing to compile - all classes are up to date.
[INFO] 
[INFO] <<< spring-boot:3.2.0:run (default-cli) < test-compile @ user-service <<<
[INFO] 
[INFO] 
[INFO] --- spring-boot:3.2.0:run (default-cli) @ user-service ---
[INFO] Attaching agents: []
[2m2025-08-02T02:09:21.099+08:00[0;39m [33m WARN[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mc.a.nacos.client.logging.NacosLogging   [0;39m [2m:[0;39m Load Logback Configuration of Nacos fail, message: Could not initialize Logback Nacos logging from classpath:nacos-logback.xml

  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
[32m :: Spring Boot :: [39m              [2m (v3.2.0)[0;39m

[2m2025-08-02T02:09:21.120+08:00[0;39m [33m WARN[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mc.a.nacos.client.logging.NacosLogging   [0;39m [2m:[0;39m Load Logback Configuration of Nacos fail, message: Could not initialize Logback Nacos logging from classpath:nacos-logback.xml
[2m2025-08-02T02:09:21.123+08:00[0;39m [32m INFO[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mc.l.user.UserServiceApplication         [0;39m [2m:[0;39m Starting UserServiceApplication using Java 23.0.2 with PID 10895 (/Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/user-service/target/classes started by jstar in /Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/user-service)
[2m2025-08-02T02:09:21.123+08:00[0;39m [32mDEBUG[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mc.l.user.UserServiceApplication         [0;39m [2m:[0;39m Running with Spring Boot v3.2.0, Spring v6.1.1
[2m2025-08-02T02:09:21.123+08:00[0;39m [32m INFO[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mc.l.user.UserServiceApplication         [0;39m [2m:[0;39m No active profile set, falling back to 1 default profile: "default"
[2m2025-08-02T02:09:21.393+08:00[0;39m [32m INFO[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mo.s.cloud.context.scope.GenericScope    [0;39m [2m:[0;39m BeanFactory id=3b5ce684-8e6f-384e-927c-7dbfedb3ca4a
[2m2025-08-02T02:09:21.496+08:00[0;39m [32m INFO[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat initialized with port 8081 (http)
[2m2025-08-02T02:09:21.500+08:00[0;39m [32m INFO[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mo.apache.catalina.core.StandardService  [0;39m [2m:[0;39m Starting service [Tomcat]
[2m2025-08-02T02:09:21.500+08:00[0;39m [32m INFO[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mo.apache.catalina.core.StandardEngine   [0;39m [2m:[0;39m Starting Servlet engine: [Apache Tomcat/10.1.16]
[2m2025-08-02T02:09:21.516+08:00[0;39m [32m INFO[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m [2m:[0;39m Initializing Spring embedded WebApplicationContext
[2m2025-08-02T02:09:21.516+08:00[0;39m [32m INFO[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mw.s.c.ServletWebServerApplicationContext[0;39m [2m:[0;39m Root WebApplicationContext: initialization completed in 371 ms
Logging initialized using 'class org.apache.ibatis.logging.stdout.StdOutImpl' adapter.
Initialization Sequence datacenterId:12 workerId:20
 _ _   |_  _ _|_. ___ _ |    _ 
| | |\/|_)(_| | |_\  |_)||_|_\ 
     /               |         
                        3.5.5 
[2m2025-08-02T02:09:21.768+08:00[0;39m [32m INFO[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat started on port 8081 (http) with context path ''
[2m2025-08-02T02:09:21.771+08:00[0;39m [33m WARN[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mc.a.nacos.client.logging.NacosLogging   [0;39m [2m:[0;39m Load Logback Configuration of Nacos fail, message: Could not initialize Logback Nacos logging from classpath:nacos-logback.xml
[2m2025-08-02T02:09:21.771+08:00[0;39m [32m INFO[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m initializer namespace from ans.namespace attribute : null
[2m2025-08-02T02:09:21.771+08:00[0;39m [32m INFO[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m initializer namespace from ALIBABA_ALIWARE_NAMESPACE attribute :null
[2m2025-08-02T02:09:21.771+08:00[0;39m [32m INFO[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m initializer namespace from namespace attribute :null
[2m2025-08-02T02:09:21.781+08:00[0;39m [32m INFO[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mc.alibaba.nacos.client.utils.ParamUtil  [0;39m [2m:[0;39m [settings] [req-serv] nacos-server port:8848
[2m2025-08-02T02:09:21.783+08:00[0;39m [32m INFO[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mc.alibaba.nacos.client.utils.ParamUtil  [0;39m [2m:[0;39m [settings] [http-client] connect timeout:1000
[2m2025-08-02T02:09:21.783+08:00[0;39m [32m INFO[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mc.alibaba.nacos.client.utils.ParamUtil  [0;39m [2m:[0;39m PER_TASK_CONFIG_SIZE: 3000.0
[2m2025-08-02T02:09:21.784+08:00[0;39m [32m INFO[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mc.a.n.p.a.s.c.ClientAuthPluginManager   [0;39m [2m:[0;39m [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[2m2025-08-02T02:09:21.785+08:00[0;39m [32m INFO[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mc.a.n.p.a.s.c.ClientAuthPluginManager   [0;39m [2m:[0;39m [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[2m2025-08-02T02:09:21.801+08:00[0;39m [32m INFO[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mc.a.n.c.a.r.identify.CredentialWatcher  [0;39m [2m:[0;39m null No credential found
[2m2025-08-02T02:09:21.804+08:00[0;39m [32m INFO[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [RpcClientFactory] create a new rpc client of 25640ef8-b217-4cfb-88ae-0585828bc060
[2m2025-08-02T02:09:21.810+08:00[0;39m [32m INFO[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [25640ef8-b217-4cfb-88ae-0585828bc060] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
[2m2025-08-02T02:09:21.810+08:00[0;39m [32m INFO[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [25640ef8-b217-4cfb-88ae-0585828bc060] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
[2m2025-08-02T02:09:21.810+08:00[0;39m [32m INFO[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [25640ef8-b217-4cfb-88ae-0585828bc060] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
[2m2025-08-02T02:09:21.811+08:00[0;39m [32m INFO[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [25640ef8-b217-4cfb-88ae-0585828bc060] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
[2m2025-08-02T02:09:21.821+08:00[0;39m [32m INFO[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mc.a.n.c.remote.client.grpc.GrpcClient   [0;39m [2m:[0;39m grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"OPENSSL","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
[2m2025-08-02T02:09:21.996+08:00[0;39m [31mERROR[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [tor-localhost-8][0;39m [2m[0;39m[36mc.a.n.c.remote.client.grpc.GrpcClient   [0;39m [2m:[0;39m [1754071761965_127.0.0.1_50781]Error to process server push response: {"headers":{},"abilityTable":{"supportPersistentInstanceByGrpc":true},"module":"internal"}
[2m2025-08-02T02:09:22.092+08:00[0;39m [32m INFO[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [25640ef8-b217-4cfb-88ae-0585828bc060] Success to connect to server [localhost:8848] on start up, connectionId = 1754071761965_127.0.0.1_50781
[2m2025-08-02T02:09:22.092+08:00[0;39m [32m INFO[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [t.remote.worker][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [25640ef8-b217-4cfb-88ae-0585828bc060] Notify connected event to listeners.
[2m2025-08-02T02:09:22.092+08:00[0;39m [32m INFO[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [t.remote.worker][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m Grpc connection connect
[2m2025-08-02T02:09:22.092+08:00[0;39m [32m INFO[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [25640ef8-b217-4cfb-88ae-0585828bc060] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
[2m2025-08-02T02:09:22.092+08:00[0;39m [32m INFO[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [25640ef8-b217-4cfb-88ae-0585828bc060] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda/0x0000000e01699340
[2m2025-08-02T02:09:22.093+08:00[0;39m [32m INFO[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m [REGISTER-SERVICE] public registering service user-service with instance Instance{instanceId='null', ip='*************', port=8081, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={IPv6=null, preserved.register.source=SPRING_CLOUD}}
[2m2025-08-02T02:09:22.100+08:00[0;39m [32m INFO[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mc.a.c.n.registry.NacosServiceRegistry   [0;39m [2m:[0;39m nacos registry, DEFAULT_GROUP user-service *************:8081 register finished
[2m2025-08-02T02:09:22.106+08:00[0;39m [32m INFO[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mc.l.user.UserServiceApplication         [0;39m [2m:[0;39m Started UserServiceApplication in 1.139 seconds (process running for 1.267)
[2m2025-08-02T02:09:24.850+08:00[0;39m [32m INFO[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [nio-8081-exec-1][0;39m [2m[0;39m[36mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m [2m:[0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
[2m2025-08-02T02:09:24.850+08:00[0;39m [32m INFO[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [nio-8081-exec-1][0;39m [2m[0;39m[36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Initializing Servlet 'dispatcherServlet'
[2m2025-08-02T02:09:24.851+08:00[0;39m [32m INFO[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [nio-8081-exec-1][0;39m [2m[0;39m[36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Completed initialization in 1 ms
[2m2025-08-02T02:10:15.839+08:00[0;39m [33m WARN[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [       Thread-7][0;39m [2m[0;39m[36mc.a.n.common.http.HttpClientBeanHolder  [0;39m [2m:[0;39m [HttpClientBeanHolder] Start destroying common HttpClient
[2m2025-08-02T02:10:15.839+08:00[0;39m [33m WARN[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [       Thread-4][0;39m [2m[0;39m[36mc.a.nacos.common.notify.NotifyCenter    [0;39m [2m:[0;39m [NotifyCenter] Start destroying Publisher
[2m2025-08-02T02:10:15.839+08:00[0;39m [33m WARN[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [       Thread-4][0;39m [2m[0;39m[36mc.a.nacos.common.notify.NotifyCenter    [0;39m [2m:[0;39m [NotifyCenter] Destruction of the end
[2m2025-08-02T02:10:15.839+08:00[0;39m [33m WARN[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [       Thread-7][0;39m [2m[0;39m[36mc.a.n.common.http.HttpClientBeanHolder  [0;39m [2m:[0;39m [HttpClientBeanHolder] Destruction of the end
[2m2025-08-02T02:10:15.843+08:00[0;39m [32m INFO[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [ionShutdownHook][0;39m [2m[0;39m[36mc.a.c.n.registry.NacosServiceRegistry   [0;39m [2m:[0;39m De-registering from Nacos Server now...
[2m2025-08-02T02:10:15.843+08:00[0;39m [32m INFO[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m [DEREGISTER-SERVICE] public deregistering service user-service with instance: Instance{instanceId='null', ip='*************', port=8081, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}
[2m2025-08-02T02:10:15.845+08:00[0;39m [32m INFO[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [ionShutdownHook][0;39m [2m[0;39m[36mc.a.c.n.registry.NacosServiceRegistry   [0;39m [2m:[0;39m De-registration finished.
[2m2025-08-02T02:10:15.845+08:00[0;39m [32m INFO[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown begin
[2m2025-08-02T02:10:15.845+08:00[0;39m [32m INFO[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
[2m2025-08-02T02:10:15.845+08:00[0;39m [32m INFO[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
[2m2025-08-02T02:10:15.845+08:00[0;39m [32m INFO[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown stop
[2m2025-08-02T02:10:15.846+08:00[0;39m [32m INFO[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate do shutdown begin
[2m2025-08-02T02:10:15.846+08:00[0;39m [32m INFO[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m com.alibaba.nacos.client.naming.core.ServiceInfoUpdateService do shutdown begin
[2m2025-08-02T02:10:15.846+08:00[0;39m [32m INFO[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m com.alibaba.nacos.client.naming.core.ServiceInfoUpdateService do shutdown stop
[2m2025-08-02T02:10:15.846+08:00[0;39m [32m INFO[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m com.alibaba.nacos.client.naming.core.ServerListManager do shutdown begin
[2m2025-08-02T02:10:15.846+08:00[0;39m [33m WARN[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m [NamingHttpClientManager] Start destroying NacosRestTemplate
[2m2025-08-02T02:10:15.846+08:00[0;39m [33m WARN[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m [NamingHttpClientManager] Destruction of the end
[2m2025-08-02T02:10:15.846+08:00[0;39m [32m INFO[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m com.alibaba.nacos.client.naming.core.ServerListManager do shutdown stop
[2m2025-08-02T02:10:15.846+08:00[0;39m [32m INFO[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m com.alibaba.nacos.client.naming.remote.http.NamingHttpClientProxy do shutdown begin
[2m2025-08-02T02:10:15.846+08:00[0;39m [33m WARN[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m [NamingHttpClientManager] Start destroying NacosRestTemplate
[2m2025-08-02T02:10:15.846+08:00[0;39m [33m WARN[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m [NamingHttpClientManager] Destruction of the end
[2m2025-08-02T02:10:15.846+08:00[0;39m [32m INFO[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m com.alibaba.nacos.client.naming.remote.http.NamingHttpClientProxy do shutdown stop
[2m2025-08-02T02:10:15.846+08:00[0;39m [32m INFO[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m Shutdown rpc client, set status to shutdown
[2m2025-08-02T02:10:15.846+08:00[0;39m [32m INFO[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@38cfecf3[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
[2m2025-08-02T02:10:15.846+08:00[0;39m [32m INFO[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m Close current connection 1754071761965_127.0.0.1_50781
[2m2025-08-02T02:10:15.846+08:00[0;39m [32m INFO[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [or-localhost-37][0;39m [2m[0;39m[36mc.a.n.c.remote.client.grpc.GrpcClient   [0;39m [2m:[0;39m [1754071761965_127.0.0.1_50781]Ignore complete event,isRunning:false,isAbandon=false
[2m2025-08-02T02:10:15.847+08:00[0;39m [32m INFO[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [ionShutdownHook][0;39m [2m[0;39m[36mc.a.n.c.remote.client.grpc.GrpcClient   [0;39m [2m:[0;39m Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@1537c744[Running, pool size = 9, active threads = 0, queued tasks = 0, completed tasks = 38]
[2m2025-08-02T02:10:15.847+08:00[0;39m [32m INFO[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m Shutdown grpc redo service executor java.util.concurrent.ScheduledThreadPoolExecutor@23ee2ccf[Running, pool size = 1, active threads = 0, queued tasks = 1, completed tasks = 17]
[2m2025-08-02T02:10:15.847+08:00[0;39m [32m INFO[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [ionShutdownHook][0;39m [2m[0;39m[36mc.a.n.c.a.r.identify.CredentialWatcher  [0;39m [2m:[0;39m [null] CredentialWatcher is stopped
[2m2025-08-02T02:10:15.847+08:00[0;39m [32m INFO[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [ionShutdownHook][0;39m [2m[0;39m[36mc.a.n.c.a.r.identify.CredentialService  [0;39m [2m:[0;39m [null] CredentialService is freed
[2m2025-08-02T02:10:15.847+08:00[0;39m [32m INFO[0;39m [35m10895[0;39m [2m---[0;39m [2m[user-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate do shutdown stop
