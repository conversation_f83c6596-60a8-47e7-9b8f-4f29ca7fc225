2025-08-02 00:00:16,685 INFO [PUSH-SUCC] 4ms, all delay time 567ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=17}, originalSize=1, DataSize=1

2025-08-02 00:04:59,974 INFO [PUSH-SUCC] 5ms, all delay time 562ms, SLA 562ms, Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=18}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-02 00:04:59,974 INFO [PUSH-SUCC] 5ms, all delay time 562ms, SLA 562ms, Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=18}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-02 00:05:10,506 INFO [PUSH-SUCC] 3ms, all delay time 565ms, SLA 565ms, Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=19}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-02 00:05:10,507 INFO [PUSH-SUCC] 4ms, all delay time 566ms, SLA 566ms, Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=19}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-02 00:06:25,664 INFO [PUSH-SUCC] 8ms, all delay time 557ms, SLA 557ms, Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=20}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-02 00:06:25,665 INFO [PUSH-SUCC] 9ms, all delay time 558ms, SLA 558ms, Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=20}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-02 00:06:37,451 INFO [PUSH-SUCC] 9ms, all delay time 522ms, SLA 522ms, Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=21}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-02 00:06:37,452 INFO [PUSH-SUCC] 11ms, all delay time 523ms, SLA 523ms, Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=21}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-02 00:09:51,697 INFO [PUSH-SUCC] 17ms, all delay time 599ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=13}, originalSize=1, DataSize=1

2025-08-02 00:11:05,043 INFO [PUSH] Task merge for Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=19}

2025-08-02 00:11:05,622 INFO [PUSH-SUCC] 3ms, all delay time 579ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='user-service', ephemeral=true, revision=7}, originalSize=1, DataSize=1

2025-08-02 00:11:05,623 INFO [PUSH-SUCC] 4ms, all delay time 580ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=13}, originalSize=1, DataSize=1

2025-08-02 00:11:05,624 INFO [PUSH-SUCC] 5ms, all delay time 581ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=13}, originalSize=1, DataSize=1

2025-08-02 00:11:05,625 INFO [PUSH-SUCC] 6ms, all delay time 582ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=21}, originalSize=1, DataSize=1

2025-08-02 00:11:05,626 INFO [PUSH-SUCC] 7ms, all delay time 611ms, SLA 612ms, Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=19}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-02 00:11:05,627 INFO [PUSH-SUCC] 8ms, all delay time 584ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=3}, originalSize=1, DataSize=1

2025-08-02 00:20:48,117 INFO [PUSH-SUCC] 5ms, all delay time 535ms, SLA 535ms, Service{namespace='public', group='DEFAULT_GROUP', name='user-service', ephemeral=true, revision=8}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-02 00:20:48,118 INFO [PUSH-SUCC] 5ms, all delay time 521ms, SLA 521ms, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=14}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-02 00:20:48,119 INFO [PUSH-SUCC] 6ms, all delay time 510ms, SLA 510ms, Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=22}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-02 00:20:48,120 INFO [PUSH-SUCC] 8ms, all delay time 523ms, SLA 523ms, Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=14}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-02 00:20:48,121 INFO [PUSH-SUCC] 9ms, all delay time 519ms, SLA 520ms, Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=4}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-02 00:22:50,515 INFO [PUSH] Task merge for Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=1}

2025-08-02 00:22:51,053 INFO [PUSH-SUCC] 8ms, all delay time 565ms, SLA 565ms, Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=1}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-02 00:22:51,056 INFO [PUSH-SUCC] 11ms, all delay time 541ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=1}, originalSize=1, DataSize=1

2025-08-02 00:24:11,047 INFO [PUSH-SUCC] 11ms, all delay time 541ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=0}, originalSize=0, DataSize=0

2025-08-02 00:25:11,399 INFO [PUSH-SUCC] 6ms, all delay time 510ms, SLA 510ms, Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=2}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-02 00:25:18,518 INFO [PUSH-SUCC] 4ms, all delay time 536ms, SLA 536ms, Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=3}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-02 00:26:28,953 INFO [PUSH-SUCC] 11ms, all delay time 573ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=0}, originalSize=0, DataSize=0

2025-08-02 00:28:19,148 INFO [PUSH-SUCC] 6ms, all delay time 537ms, SLA 537ms, Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=4}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-02 00:29:02,134 INFO [PUSH] Task merge for Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=3}

2025-08-02 00:29:02,630 INFO [PUSH-SUCC] 5ms, all delay time 525ms, SLA 525ms, Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=3}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-02 00:29:02,733 INFO [PUSH-SUCC] 4ms, all delay time 599ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=1}, originalSize=1, DataSize=1

2025-08-02 00:29:02,736 INFO [PUSH-SUCC] 7ms, all delay time 602ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=5}, originalSize=1, DataSize=1

2025-08-02 00:29:02,737 INFO [PUSH-SUCC] 8ms, all delay time 603ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='user-service', ephemeral=true, revision=1}, originalSize=1, DataSize=1

2025-08-02 00:29:02,737 INFO [PUSH-SUCC] 7ms, all delay time 603ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=1}, originalSize=1, DataSize=1

2025-08-02 00:29:02,739 INFO [PUSH-SUCC] 10ms, all delay time 605ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=1}, originalSize=1, DataSize=1

2025-08-02 00:31:17,671 INFO [PUSH-SUCC] 15ms, all delay time 587ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=5}, originalSize=1, DataSize=1

2025-08-02 00:31:19,737 INFO [PUSH-SUCC] 11ms, all delay time 533ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=1}, originalSize=1, DataSize=1

2025-08-02 00:38:40,393 INFO [PUSH-SUCC] 4ms, all delay time 596ms, SLA 596ms, Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=2}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-02 00:38:50,700 INFO [PUSH-SUCC] 4ms, all delay time 598ms, SLA 598ms, Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=3}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-02 00:54:17,831 INFO [PUSH-SUCC] 9ms, all delay time 598ms, SLA 598ms, Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=4}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-02 00:54:27,125 INFO [PUSH-SUCC] 3ms, all delay time 578ms, SLA 578ms, Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=5}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-02 01:06:33,157 INFO [PUSH-SUCC] 6ms, all delay time 598ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='payment-service', ephemeral=true, revision=1}, originalSize=1, DataSize=1

2025-08-02 01:07:11,337 INFO [PUSH-SUCC] 5ms, all delay time 581ms, SLA 581ms, Service{namespace='public', group='DEFAULT_GROUP', name='payment-service', ephemeral=true, revision=2}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-02 01:07:21,362 INFO [PUSH-SUCC] 4ms, all delay time 602ms, SLA 602ms, Service{namespace='public', group='DEFAULT_GROUP', name='payment-service', ephemeral=true, revision=3}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-02 01:08:30,017 INFO [PUSH] Task merge for Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=5}

2025-08-02 01:08:30,573 INFO [PUSH-SUCC] 13ms, all delay time 556ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=1}, originalSize=1, DataSize=1

2025-08-02 01:08:30,576 INFO [PUSH-SUCC] 16ms, all delay time 559ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=5}, originalSize=1, DataSize=1

2025-08-02 01:08:30,577 INFO [PUSH-SUCC] 17ms, all delay time 560ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=5}, originalSize=1, DataSize=1

2025-08-02 01:08:30,581 INFO [PUSH-SUCC] 21ms, all delay time 596ms, SLA 596ms, Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=5}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-02 01:08:30,583 INFO [PUSH-SUCC] 23ms, all delay time 566ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='payment-service', ephemeral=true, revision=3}, originalSize=1, DataSize=1

2025-08-02 01:08:30,585 INFO [PUSH-SUCC] 25ms, all delay time 568ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='user-service', ephemeral=true, revision=1}, originalSize=1, DataSize=1

2025-08-02 01:08:30,587 INFO [PUSH-SUCC] 26ms, all delay time 570ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=1}, originalSize=1, DataSize=1

2025-08-02 01:25:20,933 INFO [PUSH-SUCC] 6ms, all delay time 533ms, SLA 533ms, Service{namespace='public', group='DEFAULT_GROUP', name='payment-service', ephemeral=true, revision=4}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-02 01:25:29,599 INFO [PUSH-SUCC] 8ms, all delay time 593ms, SLA 593ms, Service{namespace='public', group='DEFAULT_GROUP', name='payment-service', ephemeral=true, revision=5}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-02 01:27:14,850 INFO [PUSH-SUCC] 7ms, all delay time 608ms, SLA 608ms, Service{namespace='public', group='DEFAULT_GROUP', name='payment-service', ephemeral=true, revision=6}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-02 01:27:24,235 INFO [PUSH-SUCC] 3ms, all delay time 584ms, SLA 584ms, Service{namespace='public', group='DEFAULT_GROUP', name='payment-service', ephemeral=true, revision=7}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-02 01:29:58,202 INFO [PUSH-SUCC] 8ms, all delay time 608ms, SLA 608ms, Service{namespace='public', group='DEFAULT_GROUP', name='payment-service', ephemeral=true, revision=8}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-02 01:30:07,078 INFO [PUSH-SUCC] 7ms, all delay time 551ms, SLA 552ms, Service{namespace='public', group='DEFAULT_GROUP', name='payment-service', ephemeral=true, revision=9}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-02 01:31:35,387 INFO [PUSH-SUCC] 19ms, all delay time 610ms, SLA 610ms, Service{namespace='public', group='DEFAULT_GROUP', name='payment-service', ephemeral=true, revision=10}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-02 01:31:45,086 INFO [PUSH-SUCC] 10ms, all delay time 609ms, SLA 609ms, Service{namespace='public', group='DEFAULT_GROUP', name='payment-service', ephemeral=true, revision=11}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-02 01:33:06,027 INFO [PUSH-SUCC] 8ms, all delay time 573ms, SLA 573ms, Service{namespace='public', group='DEFAULT_GROUP', name='payment-service', ephemeral=true, revision=12}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-02 01:33:16,263 INFO [PUSH-SUCC] 6ms, all delay time 599ms, SLA 599ms, Service{namespace='public', group='DEFAULT_GROUP', name='payment-service', ephemeral=true, revision=13}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-02 01:35:21,392 INFO [PUSH-SUCC] 7ms, all delay time 586ms, SLA 586ms, Service{namespace='public', group='DEFAULT_GROUP', name='payment-service', ephemeral=true, revision=14}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-02 01:35:31,501 INFO [PUSH-SUCC] 4ms, all delay time 559ms, SLA 559ms, Service{namespace='public', group='DEFAULT_GROUP', name='payment-service', ephemeral=true, revision=15}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-02 01:40:25,697 INFO [PUSH-SUCC] 4ms, all delay time 541ms, SLA 541ms, Service{namespace='public', group='DEFAULT_GROUP', name='payment-service', ephemeral=true, revision=16}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-02 01:40:53,746 INFO [PUSH-SUCC] 4ms, all delay time 570ms, SLA 570ms, Service{namespace='public', group='DEFAULT_GROUP', name='payment-service', ephemeral=true, revision=17}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-02 01:42:08,595 INFO [PUSH-SUCC] 5ms, all delay time 547ms, SLA 547ms, Service{namespace='public', group='DEFAULT_GROUP', name='payment-service', ephemeral=true, revision=18}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-02 01:42:15,926 INFO [PUSH-SUCC] 2ms, all delay time 587ms, SLA 587ms, Service{namespace='public', group='DEFAULT_GROUP', name='payment-service', ephemeral=true, revision=19}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-02 01:42:31,943 INFO [PUSH-SUCC] 1ms, all delay time 546ms, SLA 546ms, Service{namespace='public', group='DEFAULT_GROUP', name='user-service', ephemeral=true, revision=2}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-02 01:42:31,944 INFO [PUSH-SUCC] 2ms, all delay time 535ms, SLA 535ms, Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=6}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-02 01:42:31,945 INFO [PUSH-SUCC] 3ms, all delay time 530ms, SLA 530ms, Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=2}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-02 01:42:31,945 INFO [PUSH-SUCC] 3ms, all delay time 510ms, SLA 510ms, Service{namespace='public', group='DEFAULT_GROUP', name='payment-service', ephemeral=true, revision=20}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-02 01:42:31,946 INFO [PUSH-SUCC] 4ms, all delay time 519ms, SLA 519ms, Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=6}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-02 01:42:31,947 INFO [PUSH-SUCC] 5ms, all delay time 542ms, SLA 542ms, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=2}, originalSize=0, DataSize=0, target=192.168.0.110

