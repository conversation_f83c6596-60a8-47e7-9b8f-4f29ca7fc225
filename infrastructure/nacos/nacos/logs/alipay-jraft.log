2025-08-02 00:02:19,841 INFO Truncated prefix logs in data path: /Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/infrastructure/nacos/nacos/data/protocol/raft/naming_service_metadata/log from log index 2 to 2, cost 0 ms.

2025-08-02 00:22:53,020 INFO Truncated prefix logs in data path: /Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/infrastructure/nacos/nacos/data/protocol/raft/naming_persistent_service_v2/log from log index 2 to 2, cost 1 ms.

2025-08-02 00:24:07,366 INFO Truncated prefix logs in data path: /Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/infrastructure/nacos/nacos/data/protocol/raft/naming_instance_metadata/log from log index 2 to 2, cost 0 ms.

2025-08-02 00:32:19,769 INFO Truncated prefix logs in data path: /Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/infrastructure/nacos/nacos/data/protocol/raft/naming_service_metadata/log from log index 2 to 2, cost 0 ms.

2025-08-02 00:52:53,065 INFO Truncated prefix logs in data path: /Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/infrastructure/nacos/nacos/data/protocol/raft/naming_persistent_service_v2/log from log index 2 to 2, cost 0 ms.

2025-08-02 00:54:07,410 INFO Truncated prefix logs in data path: /Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/infrastructure/nacos/nacos/data/protocol/raft/naming_instance_metadata/log from log index 2 to 2, cost 0 ms.

2025-08-02 01:02:19,834 INFO Truncated prefix logs in data path: /Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/infrastructure/nacos/nacos/data/protocol/raft/naming_service_metadata/log from log index 2 to 2, cost 0 ms.

2025-08-02 01:22:52,969 INFO Truncated prefix logs in data path: /Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/infrastructure/nacos/nacos/data/protocol/raft/naming_persistent_service_v2/log from log index 2 to 2, cost 0 ms.

2025-08-02 01:24:07,305 INFO Truncated prefix logs in data path: /Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/infrastructure/nacos/nacos/data/protocol/raft/naming_instance_metadata/log from log index 2 to 2, cost 0 ms.

2025-08-02 01:32:19,703 INFO Truncated prefix logs in data path: /Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/infrastructure/nacos/nacos/data/protocol/raft/naming_service_metadata/log from log index 2 to 2, cost 0 ms.

2025-08-02 01:52:52,942 INFO Truncated prefix logs in data path: /Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/infrastructure/nacos/nacos/data/protocol/raft/naming_persistent_service_v2/log from log index 2 to 2, cost 0 ms.

2025-08-02 01:54:07,292 INFO Truncated prefix logs in data path: /Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/infrastructure/nacos/nacos/data/protocol/raft/naming_instance_metadata/log from log index 2 to 2, cost 0 ms.

2025-08-02 02:02:19,766 INFO Truncated prefix logs in data path: /Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/infrastructure/nacos/nacos/data/protocol/raft/naming_service_metadata/log from log index 2 to 2, cost 0 ms.

2025-08-02 02:10:16,301 INFO Node <naming_service_metadata/*************:7848> shutdown, currTerm=1 state=STATE_LEADER.

2025-08-02 02:10:16,302 INFO Fail to find the next candidate.

2025-08-02 02:10:16,302 INFO onLeaderStop: status=Status[ESHUTDOWN<1007>: Raft node is going to quit.].

2025-08-02 02:10:16,315 INFO Save raft meta, path=/Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/infrastructure/nacos/nacos/data/protocol/raft/naming_service_metadata/meta-data, term=1, votedFor=*************:7848, cost time=12 ms

2025-08-02 02:10:16,315 INFO Shutting down FSMCaller...

2025-08-02 02:10:16,315 INFO onShutdown.

2025-08-02 02:10:16,315 INFO ThreadPool is terminated: JRaft-RPC-Processor, com.alipay.sofa.jraft.util.MetricThreadPoolExecutor@7c663eaf[Shutting down, pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0].

2025-08-02 02:10:16,315 INFO ThreadPool is terminated: JRaft-Node-ScheduleThreadPool, com.alipay.sofa.jraft.util.MetricScheduledThreadPoolExecutor@12a2585b[Shutting down, pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0].

2025-08-02 02:10:16,315 INFO Destroy timer: RepeatedTimer{timeout=null, stopped=true, running=false, destroyed=true, invoking=false, timeoutMs=5000, name='JRaft-ElectionTimer-<naming_service_metadata/*************:7848>'}.

2025-08-02 02:10:16,315 INFO Destroy timer: RepeatedTimer{timeout=null, stopped=true, running=false, destroyed=true, invoking=false, timeoutMs=5000, name='JRaft-VoteTimer-<naming_service_metadata/*************:7848>'}.

2025-08-02 02:10:16,315 INFO Destroy timer: RepeatedTimer{timeout=null, stopped=true, running=false, destroyed=true, invoking=false, timeoutMs=2500, name='JRaft-StepDownTimer-<naming_service_metadata/*************:7848>'}.

2025-08-02 02:10:16,315 INFO Destroy timer: RepeatedTimer{timeout=null, stopped=true, running=false, destroyed=true, invoking=false, timeoutMs=1800000, name='JRaft-SnapshotTimer-<naming_service_metadata/*************:7848>'}.

2025-08-02 02:10:16,315 INFO The number of active nodes decrement to 2.

2025-08-02 02:10:16,315 INFO Node <naming_service_metadata/*************:7848> shutdown, currTerm=1 state=STATE_SHUTTING.

2025-08-02 02:10:16,315 INFO Stop the RaftGroupService successfully.

2025-08-02 02:10:16,315 INFO Node <naming_instance_metadata/*************:7848> shutdown, currTerm=1 state=STATE_LEADER.

2025-08-02 02:10:16,315 INFO Fail to find the next candidate.

2025-08-02 02:10:16,316 INFO onLeaderStop: status=Status[ESHUTDOWN<1007>: Raft node is going to quit.].

2025-08-02 02:10:16,318 INFO DB destroyed, the db path is: /Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/infrastructure/nacos/nacos/data/protocol/raft/naming_service_metadata/log.

2025-08-02 02:10:16,330 INFO Save raft meta, path=/Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/infrastructure/nacos/nacos/data/protocol/raft/naming_instance_metadata/meta-data, term=1, votedFor=*************:7848, cost time=14 ms

2025-08-02 02:10:16,330 INFO Shutting down FSMCaller...

2025-08-02 02:10:16,330 INFO onShutdown.

2025-08-02 02:10:16,330 INFO ThreadPool is terminated: JRaft-RPC-Processor, com.alipay.sofa.jraft.util.MetricThreadPoolExecutor@479111ba[Shutting down, pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0].

2025-08-02 02:10:16,330 INFO ThreadPool is terminated: JRaft-Node-ScheduleThreadPool, com.alipay.sofa.jraft.util.MetricScheduledThreadPoolExecutor@6690b9fa[Shutting down, pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0].

2025-08-02 02:10:16,330 INFO Destroy timer: RepeatedTimer{timeout=null, stopped=true, running=false, destroyed=true, invoking=false, timeoutMs=5000, name='JRaft-ElectionTimer-<naming_instance_metadata/*************:7848>'}.

2025-08-02 02:10:16,330 INFO Destroy timer: RepeatedTimer{timeout=null, stopped=true, running=false, destroyed=true, invoking=false, timeoutMs=5000, name='JRaft-VoteTimer-<naming_instance_metadata/*************:7848>'}.

2025-08-02 02:10:16,330 INFO Destroy timer: RepeatedTimer{timeout=null, stopped=true, running=false, destroyed=true, invoking=false, timeoutMs=2500, name='JRaft-StepDownTimer-<naming_instance_metadata/*************:7848>'}.

2025-08-02 02:10:16,330 INFO The number of active nodes decrement to 1.

2025-08-02 02:10:16,330 INFO Destroy timer: RepeatedTimer{timeout=null, stopped=true, running=false, destroyed=true, invoking=false, timeoutMs=1800000, name='JRaft-SnapshotTimer-<naming_instance_metadata/*************:7848>'}.

2025-08-02 02:10:16,330 INFO Node <naming_instance_metadata/*************:7848> shutdown, currTerm=1 state=STATE_SHUTTING.

2025-08-02 02:10:16,330 INFO Stop the RaftGroupService successfully.

2025-08-02 02:10:16,330 INFO Node <naming_persistent_service_v2/*************:7848> shutdown, currTerm=1 state=STATE_LEADER.

2025-08-02 02:10:16,330 INFO Fail to find the next candidate.

2025-08-02 02:10:16,330 INFO onLeaderStop: status=Status[ESHUTDOWN<1007>: Raft node is going to quit.].

2025-08-02 02:10:16,332 INFO DB destroyed, the db path is: /Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/infrastructure/nacos/nacos/data/protocol/raft/naming_instance_metadata/log.

2025-08-02 02:10:16,341 INFO Save raft meta, path=/Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/infrastructure/nacos/nacos/data/protocol/raft/naming_persistent_service_v2/meta-data, term=1, votedFor=*************:7848, cost time=10 ms

2025-08-02 02:10:16,341 INFO Shutting down FSMCaller...

2025-08-02 02:10:16,341 INFO onShutdown.

2025-08-02 02:10:16,341 INFO ThreadPool is terminated: JRaft-RPC-Processor, com.alipay.sofa.jraft.util.MetricThreadPoolExecutor@d535a3d[Shutting down, pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0].

2025-08-02 02:10:16,341 INFO ThreadPool is terminated: JRaft-Node-ScheduleThreadPool, com.alipay.sofa.jraft.util.MetricScheduledThreadPoolExecutor@25d0cb3a[Shutting down, pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0].

2025-08-02 02:10:16,341 INFO The number of active nodes decrement to 0.

2025-08-02 02:10:16,342 INFO ThreadPool is terminated: JRaft-Global-ElectionTimer, com.alipay.sofa.jraft.util.MetricScheduledThreadPoolExecutor@235d659c[Shutting down, pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 3].

2025-08-02 02:10:16,342 INFO Destroy timer: RepeatedTimer{timeout=null, stopped=true, running=false, destroyed=true, invoking=false, timeoutMs=5000, name='JRaft-ElectionTimer-<naming_persistent_service_v2/*************:7848>'}.

2025-08-02 02:10:16,342 INFO ThreadPool is terminated: JRaft-Global-VoteTimer, com.alipay.sofa.jraft.util.MetricScheduledThreadPoolExecutor@2b2954e1[Shutting down, pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 3].

2025-08-02 02:10:16,342 INFO Destroy timer: RepeatedTimer{timeout=null, stopped=true, running=false, destroyed=true, invoking=false, timeoutMs=5000, name='JRaft-VoteTimer-<naming_persistent_service_v2/*************:7848>'}.

2025-08-02 02:10:16,342 INFO ThreadPool is terminated: JRaft-Global-StepDownTimer, com.alipay.sofa.jraft.util.MetricScheduledThreadPoolExecutor@f5ce0bb[Shutting down, pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 47733].

2025-08-02 02:10:16,342 INFO Destroy timer: RepeatedTimer{timeout=null, stopped=true, running=false, destroyed=true, invoking=false, timeoutMs=2500, name='JRaft-StepDownTimer-<naming_persistent_service_v2/*************:7848>'}.

2025-08-02 02:10:16,343 INFO ThreadPool is terminated: JRaft-Global-SnapshotTimer, com.alipay.sofa.jraft.util.MetricScheduledThreadPoolExecutor@360e9c06[Shutting down, pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 66].

2025-08-02 02:10:16,343 INFO Destroy timer: RepeatedTimer{timeout=null, stopped=true, running=false, destroyed=true, invoking=false, timeoutMs=1800000, name='JRaft-SnapshotTimer-<naming_persistent_service_v2/*************:7848>'}.

2025-08-02 02:10:16,343 INFO Node <naming_persistent_service_v2/*************:7848> shutdown, currTerm=1 state=STATE_SHUTTING.

2025-08-02 02:10:16,343 INFO Stop the RaftGroupService successfully.

2025-08-02 02:10:16,343 INFO Shutdown managed channel: *************:7848, ManagedChannelOrphanWrapper{delegate=ManagedChannelImpl{logId=10, target=*************:7848}}.

2025-08-02 02:10:16,343 INFO The channel *************:7848 is in state: SHUTDOWN.

2025-08-02 02:10:16,343 WARN This channel *************:7848 has started shutting down. Any new RPCs should fail immediately.

2025-08-02 02:10:16,345 INFO DB destroyed, the db path is: /Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/infrastructure/nacos/nacos/data/protocol/raft/naming_persistent_service_v2/log.

2025-08-02 02:10:16,345 INFO Connection disconnected: /*************:52080

2025-08-02 02:10:16,346 INFO ThreadPool is terminated: JRaft-RPC-Processor, com.alipay.sofa.jraft.util.MetricThreadPoolExecutor@3bec5821[Shutting down, pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 35358].

