2025-08-02 00:04:59,412 INFO Client connection 1754058863006_127.0.0.1_63254 disconnect, remove instances and subscribers

2025-08-02 00:05:09,831 INFO Client connection 1754064309804_127.0.0.1_63213 connect

2025-08-02 00:05:09,941 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=18}, 1754064309804_127.0.0.1_63213

2025-08-02 00:06:25,107 INFO Client connection 1754064309804_127.0.0.1_63213 disconnect, remove instances and subscribers

2025-08-02 00:06:36,816 INFO Client connection 1754064396786_127.0.0.1_63717 connect

2025-08-02 00:06:36,929 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=20}, 1754064396786_127.0.0.1_63717

2025-08-02 00:10:57,003 INFO Client connection 1754057710652_127.0.0.1_58891 disconnect, remove instances and subscribers

2025-08-02 00:11:04,897 INFO Client connection 1754064664870_127.0.0.1_65169 connect

2025-08-02 00:11:05,014 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=18}, 1754064664870_127.0.0.1_65169

2025-08-02 00:20:47,581 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='user-service', ephemeral=true, revision=7}, 1754055759425_127.0.0.1_64131

2025-08-02 00:20:47,585 INFO Client connection 1754055759425_127.0.0.1_64131 disconnect, remove instances and subscribers

2025-08-02 00:20:47,597 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=13}, 1754062394721_127.0.0.1_59464

2025-08-02 00:20:47,597 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=13}, 1754062475327_127.0.0.1_59592

2025-08-02 00:20:47,600 INFO Client connection 1754062394721_127.0.0.1_59464 disconnect, remove instances and subscribers

2025-08-02 00:20:47,600 INFO Client connection 1754062475327_127.0.0.1_59592 disconnect, remove instances and subscribers

2025-08-02 00:20:47,601 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=3}, 1754063129068_127.0.0.1_60434

2025-08-02 00:20:47,606 INFO Client connection 1754063129068_127.0.0.1_60434 disconnect, remove instances and subscribers

2025-08-02 00:20:47,609 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=21}, 1754064396786_127.0.0.1_63717

2025-08-02 00:20:47,615 INFO Client connection 1754064396786_127.0.0.1_63717 disconnect, remove instances and subscribers

2025-08-02 00:20:49,689 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=19}, 1754064664870_127.0.0.1_65169

2025-08-02 00:20:49,696 INFO Client connection 1754064664870_127.0.0.1_65169 disconnect, remove instances and subscribers

2025-08-02 00:21:53,131 WARN namespace : public, [DEFAULT_GROUP@@discussion-service] services are automatically cleaned

2025-08-02 00:21:53,131 WARN namespace : public, [DEFAULT_GROUP@@user-service] services are automatically cleaned

2025-08-02 00:21:53,131 WARN namespace : public, [DEFAULT_GROUP@@recommendation-service] services are automatically cleaned

2025-08-02 00:21:53,131 WARN namespace : public, [DEFAULT_GROUP@@gateway-service] services are automatically cleaned

2025-08-02 00:21:53,131 WARN namespace : public, [DEFAULT_GROUP@@learning-service] services are automatically cleaned

2025-08-02 00:21:53,131 WARN namespace : public, [DEFAULT_GROUP@@course-service] services are automatically cleaned

2025-08-02 00:22:48,056 INFO Client connection 1754065368019_127.0.0.1_52593 connect

2025-08-02 00:22:48,167 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=0}, 1754065368019_127.0.0.1_52593

2025-08-02 00:22:50,374 INFO Client connection 1754065370344_127.0.0.1_52608 connect

2025-08-02 00:22:50,488 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=0}, 1754065370344_127.0.0.1_52608

2025-08-02 00:25:10,889 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=1}, 1754065368019_127.0.0.1_52593

2025-08-02 00:25:10,893 INFO Client connection 1754065368019_127.0.0.1_52593 disconnect, remove instances and subscribers

2025-08-02 00:25:17,868 INFO Client connection 1754065517844_127.0.0.1_53417 connect

2025-08-02 00:25:17,982 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=2}, 1754065517844_127.0.0.1_53417

2025-08-02 00:28:18,611 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=3}, 1754065517844_127.0.0.1_53417

2025-08-02 00:28:18,615 INFO Client connection 1754065517844_127.0.0.1_53417 disconnect, remove instances and subscribers

2025-08-02 00:28:21,834 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=1}, 1754065370344_127.0.0.1_52608

2025-08-02 00:28:21,841 INFO Client connection 1754065370344_127.0.0.1_52608 disconnect, remove instances and subscribers

2025-08-02 00:28:37,032 INFO Client connection 1754065717007_127.0.0.1_54379 connect

2025-08-02 00:28:37,146 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='user-service', ephemeral=true, revision=0}, 1754065717007_127.0.0.1_54379

2025-08-02 00:28:43,184 INFO Client connection 1754065723160_127.0.0.1_54390 connect

2025-08-02 00:28:43,297 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=0}, 1754065723160_127.0.0.1_54390

2025-08-02 00:28:48,746 INFO Client connection 1754065728719_127.0.0.1_54414 connect

2025-08-02 00:28:48,853 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=4}, 1754065728719_127.0.0.1_54414

2025-08-02 00:28:52,344 INFO Client connection 1754065732317_127.0.0.1_54420 connect

2025-08-02 00:28:52,453 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=0}, 1754065732317_127.0.0.1_54420

2025-08-02 00:28:57,501 INFO Client connection 1754065737474_127.0.0.1_54435 connect

2025-08-02 00:28:57,611 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=0}, 1754065737474_127.0.0.1_54435

2025-08-02 00:29:01,993 INFO Client connection 1754065741963_127.0.0.1_54444 connect

2025-08-02 00:29:02,105 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=2}, 1754065741963_127.0.0.1_54444

2025-08-02 00:29:39,179 ERROR [TASK-FAILED] java.lang.InterruptedException

java.lang.InterruptedException: null
	at java.base/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1727)
	at java.base/java.util.concurrent.ArrayBlockingQueue.take(ArrayBlockingQueue.java:420)
	at com.alibaba.nacos.common.task.engine.TaskExecuteWorker$InnerWorker.run(TaskExecuteWorker.java:118)
2025-08-02 00:29:39,179 ERROR [TASK-FAILED] java.lang.InterruptedException

java.lang.InterruptedException: null
	at java.base/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1727)
	at java.base/java.util.concurrent.ArrayBlockingQueue.take(ArrayBlockingQueue.java:420)
	at com.alibaba.nacos.common.task.engine.TaskExecuteWorker$InnerWorker.run(TaskExecuteWorker.java:118)
2025-08-02 00:29:39,179 ERROR [TASK-FAILED] java.lang.InterruptedException

java.lang.InterruptedException: null
	at java.base/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1727)
	at java.base/java.util.concurrent.ArrayBlockingQueue.take(ArrayBlockingQueue.java:420)
	at com.alibaba.nacos.common.task.engine.TaskExecuteWorker$InnerWorker.run(TaskExecuteWorker.java:118)
2025-08-02 00:29:39,179 ERROR [TASK-FAILED] java.lang.InterruptedException

java.lang.InterruptedException: null
	at java.base/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1727)
	at java.base/java.util.concurrent.ArrayBlockingQueue.take(ArrayBlockingQueue.java:420)
	at com.alibaba.nacos.common.task.engine.TaskExecuteWorker$InnerWorker.run(TaskExecuteWorker.java:118)
2025-08-02 00:29:39,179 ERROR [TASK-FAILED] java.lang.InterruptedException

java.lang.InterruptedException: null
	at java.base/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1727)
	at java.base/java.util.concurrent.ArrayBlockingQueue.take(ArrayBlockingQueue.java:420)
	at com.alibaba.nacos.common.task.engine.TaskExecuteWorker$InnerWorker.run(TaskExecuteWorker.java:118)
2025-08-02 00:29:39,179 ERROR [TASK-FAILED] java.lang.InterruptedException

java.lang.InterruptedException: null
	at java.base/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1727)
	at java.base/java.util.concurrent.ArrayBlockingQueue.take(ArrayBlockingQueue.java:420)
	at com.alibaba.nacos.common.task.engine.TaskExecuteWorker$InnerWorker.run(TaskExecuteWorker.java:118)
2025-08-02 00:29:39,179 ERROR [TASK-FAILED] java.lang.InterruptedException

java.lang.InterruptedException: null
	at java.base/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1727)
	at java.base/java.util.concurrent.ArrayBlockingQueue.take(ArrayBlockingQueue.java:420)
	at com.alibaba.nacos.common.task.engine.TaskExecuteWorker$InnerWorker.run(TaskExecuteWorker.java:118)
2025-08-02 00:29:39,179 ERROR [TASK-FAILED] java.lang.InterruptedException

java.lang.InterruptedException: null
	at java.base/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1727)
	at java.base/java.util.concurrent.ArrayBlockingQueue.take(ArrayBlockingQueue.java:420)
	at com.alibaba.nacos.common.task.engine.TaskExecuteWorker$InnerWorker.run(TaskExecuteWorker.java:118)
2025-08-02 00:29:39,179 ERROR [TASK-FAILED] java.lang.InterruptedException

java.lang.InterruptedException: null
	at java.base/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1727)
	at java.base/java.util.concurrent.ArrayBlockingQueue.take(ArrayBlockingQueue.java:420)
	at com.alibaba.nacos.common.task.engine.TaskExecuteWorker$InnerWorker.run(TaskExecuteWorker.java:118)
2025-08-02 00:29:39,179 ERROR [TASK-FAILED] java.lang.InterruptedException

java.lang.InterruptedException: null
	at java.base/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1727)
	at java.base/java.util.concurrent.ArrayBlockingQueue.take(ArrayBlockingQueue.java:420)
	at com.alibaba.nacos.common.task.engine.TaskExecuteWorker$InnerWorker.run(TaskExecuteWorker.java:118)
2025-08-02 00:29:39,179 ERROR [TASK-FAILED] java.lang.InterruptedException

java.lang.InterruptedException: null
	at java.base/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1727)
	at java.base/java.util.concurrent.ArrayBlockingQueue.take(ArrayBlockingQueue.java:420)
	at com.alibaba.nacos.common.task.engine.TaskExecuteWorker$InnerWorker.run(TaskExecuteWorker.java:118)
2025-08-02 00:29:39,179 ERROR [TASK-FAILED] java.lang.InterruptedException

java.lang.InterruptedException: null
	at java.base/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1727)
	at java.base/java.util.concurrent.ArrayBlockingQueue.take(ArrayBlockingQueue.java:420)
	at com.alibaba.nacos.common.task.engine.TaskExecuteWorker$InnerWorker.run(TaskExecuteWorker.java:118)
2025-08-02 00:29:39,179 ERROR [TASK-FAILED] java.lang.InterruptedException

java.lang.InterruptedException: null
	at java.base/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1727)
	at java.base/java.util.concurrent.ArrayBlockingQueue.take(ArrayBlockingQueue.java:420)
	at com.alibaba.nacos.common.task.engine.TaskExecuteWorker$InnerWorker.run(TaskExecuteWorker.java:118)
2025-08-02 00:29:39,179 ERROR [TASK-FAILED] java.lang.InterruptedException

java.lang.InterruptedException: null
	at java.base/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1727)
	at java.base/java.util.concurrent.ArrayBlockingQueue.take(ArrayBlockingQueue.java:420)
	at com.alibaba.nacos.common.task.engine.TaskExecuteWorker$InnerWorker.run(TaskExecuteWorker.java:118)
2025-08-02 00:29:39,179 ERROR [TASK-FAILED] java.lang.InterruptedException

java.lang.InterruptedException: null
	at java.base/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1727)
	at java.base/java.util.concurrent.ArrayBlockingQueue.take(ArrayBlockingQueue.java:420)
	at com.alibaba.nacos.common.task.engine.TaskExecuteWorker$InnerWorker.run(TaskExecuteWorker.java:118)
2025-08-02 00:29:39,179 ERROR [TASK-FAILED] java.lang.InterruptedException

java.lang.InterruptedException: null
	at java.base/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1727)
	at java.base/java.util.concurrent.ArrayBlockingQueue.take(ArrayBlockingQueue.java:420)
	at com.alibaba.nacos.common.task.engine.TaskExecuteWorker$InnerWorker.run(TaskExecuteWorker.java:118)
2025-08-02 00:38:39,797 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=1}, 1754065737474_127.0.0.1_54435

2025-08-02 00:38:39,801 INFO Client connection 1754065737474_127.0.0.1_54435 disconnect, remove instances and subscribers

2025-08-02 00:38:49,988 INFO Client connection 1754066329962_127.0.0.1_55627 connect

2025-08-02 00:38:50,102 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=2}, 1754066329962_127.0.0.1_55627

2025-08-02 00:54:17,233 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=3}, 1754066329962_127.0.0.1_55627

2025-08-02 00:54:17,237 INFO Client connection 1754066329962_127.0.0.1_55627 disconnect, remove instances and subscribers

2025-08-02 00:54:26,436 INFO Client connection 1754067266410_127.0.0.1_58975 connect

2025-08-02 00:54:26,547 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=4}, 1754067266410_127.0.0.1_58975

2025-08-02 01:06:11,748 INFO Client connection 1754067971724_127.0.0.1_62925 connect

2025-08-02 01:06:11,861 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='payment-service', ephemeral=true, revision=0}, 1754067971724_127.0.0.1_62925

2025-08-02 01:07:10,756 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='payment-service', ephemeral=true, revision=1}, 1754067971724_127.0.0.1_62925

2025-08-02 01:07:10,760 INFO Client connection 1754067971724_127.0.0.1_62925 disconnect, remove instances and subscribers

2025-08-02 01:07:20,646 INFO Client connection 1754068040621_127.0.0.1_63303 connect

2025-08-02 01:07:20,759 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='payment-service', ephemeral=true, revision=2}, 1754068040621_127.0.0.1_63303

2025-08-02 01:08:22,749 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=3}, 1754065741963_127.0.0.1_54444

2025-08-02 01:08:22,758 INFO Client connection 1754065741963_127.0.0.1_54444 disconnect, remove instances and subscribers

2025-08-02 01:08:29,873 INFO Client connection 1754068109849_127.0.0.1_63664 connect

2025-08-02 01:08:29,985 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=4}, 1754068109849_127.0.0.1_63664

2025-08-02 01:25:20,400 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='payment-service', ephemeral=true, revision=3}, 1754068040621_127.0.0.1_63303

2025-08-02 01:25:20,403 INFO Client connection 1754068040621_127.0.0.1_63303 disconnect, remove instances and subscribers

2025-08-02 01:25:28,893 INFO Client connection 1754069128870_127.0.0.1_52653 connect

2025-08-02 01:25:29,006 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='payment-service', ephemeral=true, revision=4}, 1754069128870_127.0.0.1_52653

2025-08-02 01:27:14,242 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='payment-service', ephemeral=true, revision=5}, 1754069128870_127.0.0.1_52653

2025-08-02 01:27:14,246 INFO Client connection 1754069128870_127.0.0.1_52653 disconnect, remove instances and subscribers

2025-08-02 01:27:23,538 INFO Client connection 1754069243514_127.0.0.1_53291 connect

2025-08-02 01:27:23,650 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='payment-service', ephemeral=true, revision=6}, 1754069243514_127.0.0.1_53291

2025-08-02 01:29:57,594 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='payment-service', ephemeral=true, revision=7}, 1754069243514_127.0.0.1_53291

2025-08-02 01:29:57,599 INFO Client connection 1754069243514_127.0.0.1_53291 disconnect, remove instances and subscribers

2025-08-02 01:30:06,415 INFO Client connection 1754069406390_127.0.0.1_54148 connect

2025-08-02 01:30:06,526 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='payment-service', ephemeral=true, revision=8}, 1754069406390_127.0.0.1_54148

2025-08-02 01:31:34,776 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='payment-service', ephemeral=true, revision=9}, 1754069406390_127.0.0.1_54148

2025-08-02 01:31:34,780 INFO Client connection 1754069406390_127.0.0.1_54148 disconnect, remove instances and subscribers

2025-08-02 01:31:44,368 INFO Client connection 1754069504344_127.0.0.1_54678 connect

2025-08-02 01:31:44,477 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='payment-service', ephemeral=true, revision=10}, 1754069504344_127.0.0.1_54678

2025-08-02 01:33:05,454 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='payment-service', ephemeral=true, revision=11}, 1754069504344_127.0.0.1_54678

2025-08-02 01:33:05,459 INFO Client connection 1754069504344_127.0.0.1_54678 disconnect, remove instances and subscribers

2025-08-02 01:33:15,551 INFO Client connection 1754069595528_127.0.0.1_55165 connect

2025-08-02 01:33:15,663 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='payment-service', ephemeral=true, revision=12}, 1754069595528_127.0.0.1_55165

2025-08-02 01:35:20,805 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='payment-service', ephemeral=true, revision=13}, 1754069595528_127.0.0.1_55165

2025-08-02 01:35:20,810 INFO Client connection 1754069595528_127.0.0.1_55165 disconnect, remove instances and subscribers

2025-08-02 01:35:30,831 INFO Client connection 1754069730807_127.0.0.1_55888 connect

2025-08-02 01:35:30,942 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='payment-service', ephemeral=true, revision=14}, 1754069730807_127.0.0.1_55888

2025-08-02 01:40:25,155 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='payment-service', ephemeral=true, revision=15}, 1754069730807_127.0.0.1_55888

2025-08-02 01:40:25,159 INFO Client connection 1754069730807_127.0.0.1_55888 disconnect, remove instances and subscribers

2025-08-02 01:40:53,065 INFO Client connection 1754070053024_127.0.0.1_57666 connect

2025-08-02 01:40:53,176 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='payment-service', ephemeral=true, revision=16}, 1754070053024_127.0.0.1_57666

2025-08-02 01:42:08,047 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='payment-service', ephemeral=true, revision=17}, 1754070053024_127.0.0.1_57666

2025-08-02 01:42:08,051 INFO Client connection 1754070053024_127.0.0.1_57666 disconnect, remove instances and subscribers

2025-08-02 01:42:15,227 INFO Client connection 1754070135198_127.0.0.1_58131 connect

2025-08-02 01:42:15,339 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='payment-service', ephemeral=true, revision=18}, 1754070135198_127.0.0.1_58131

2025-08-02 01:42:31,397 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='user-service', ephemeral=true, revision=1}, 1754065717007_127.0.0.1_54379

2025-08-02 01:42:31,400 INFO Client connection 1754065717007_127.0.0.1_54379 disconnect, remove instances and subscribers

2025-08-02 01:42:31,405 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=1}, 1754065723160_127.0.0.1_54390

2025-08-02 01:42:31,408 INFO Client connection 1754065723160_127.0.0.1_54390 disconnect, remove instances and subscribers

2025-08-02 01:42:31,409 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=5}, 1754065728719_127.0.0.1_54414

2025-08-02 01:42:31,412 INFO Client connection 1754065728719_127.0.0.1_54414 disconnect, remove instances and subscribers

2025-08-02 01:42:31,414 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=1}, 1754065732317_127.0.0.1_54420

2025-08-02 01:42:31,421 INFO Client connection 1754065732317_127.0.0.1_54420 disconnect, remove instances and subscribers

2025-08-02 01:42:31,426 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=5}, 1754067266410_127.0.0.1_58975

2025-08-02 01:42:31,434 INFO Client connection 1754067266410_127.0.0.1_58975 disconnect, remove instances and subscribers

2025-08-02 01:42:31,434 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='payment-service', ephemeral=true, revision=19}, 1754070135198_127.0.0.1_58131

2025-08-02 01:42:31,438 INFO Client connection 1754070135198_127.0.0.1_58131 disconnect, remove instances and subscribers

2025-08-02 01:42:33,501 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=5}, 1754068109849_127.0.0.1_63664

2025-08-02 01:42:33,503 INFO Client connection 1754068109849_127.0.0.1_63664 disconnect, remove instances and subscribers

2025-08-02 01:43:53,323 WARN namespace : public, [DEFAULT_GROUP@@discussion-service] services are automatically cleaned

2025-08-02 01:43:53,323 WARN namespace : public, [DEFAULT_GROUP@@user-service] services are automatically cleaned

2025-08-02 01:43:53,323 WARN namespace : public, [DEFAULT_GROUP@@recommendation-service] services are automatically cleaned

2025-08-02 01:43:53,323 WARN namespace : public, [DEFAULT_GROUP@@gateway-service] services are automatically cleaned

2025-08-02 01:43:53,323 WARN namespace : public, [DEFAULT_GROUP@@payment-service] services are automatically cleaned

2025-08-02 01:43:53,323 WARN namespace : public, [DEFAULT_GROUP@@learning-service] services are automatically cleaned

2025-08-02 01:43:53,323 WARN namespace : public, [DEFAULT_GROUP@@course-service] services are automatically cleaned

2025-08-02 01:48:49,406 INFO Client connection 1754070529382_127.0.0.1_60400 connect

2025-08-02 01:48:49,519 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=0}, 1754070529382_127.0.0.1_60400

2025-08-02 01:51:53,530 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=1}, 1754070529382_127.0.0.1_60400

2025-08-02 01:51:53,534 INFO Client connection 1754070529382_127.0.0.1_60400 disconnect, remove instances and subscribers

2025-08-02 01:52:02,797 INFO Client connection 1754070722772_127.0.0.1_61588 connect

2025-08-02 01:52:02,909 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=2}, 1754070722772_127.0.0.1_61588

2025-08-02 01:59:16,793 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=3}, 1754070722772_127.0.0.1_61588

2025-08-02 01:59:16,797 INFO Client connection 1754070722772_127.0.0.1_61588 disconnect, remove instances and subscribers

2025-08-02 02:00:53,452 WARN namespace : public, [DEFAULT_GROUP@@recommendation-service] services are automatically cleaned

2025-08-02 02:09:21,992 INFO Client connection 1754071761965_127.0.0.1_50781 connect

2025-08-02 02:09:22,099 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='user-service', ephemeral=true, revision=0}, 1754071761965_127.0.0.1_50781

2025-08-02 02:10:15,844 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='user-service', ephemeral=true, revision=1}, 1754071761965_127.0.0.1_50781

2025-08-02 02:10:15,848 INFO Client connection 1754071761965_127.0.0.1_50781 disconnect, remove instances and subscribers

2025-08-02 02:10:16,269 WARN [NamingServerHttpClientManager] Start destroying HTTP-Client

2025-08-02 02:10:16,279 WARN [NamingServerHttpClientManager] Destruction of the end

